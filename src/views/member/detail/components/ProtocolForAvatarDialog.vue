<template>
  <Modal v-model="show" title="会员人脸识别使用告知" width="680" :mask-closable="false" :closable="true" @on-cancel="handleClose">
    <div class="protocol-content">
      <!-- 隐私政策说明 -->
      <div class="privacy-notice">
        依据国家《个人隐私保护法》规定，采集人脸信息前须向用户说明用途，并取得用户同意。
      </div>

      <!-- 协议内容 -->
      <div class="protocol-sections">
        <div v-html="protocol + protocol + protocol + protocol"></div>
      </div>

      <!-- 底部提示 -->
      <div class="bottom-notice">
        请务必向《会员小程序》自行上传头像。
      </div>
    </div>

    <div slot="footer" class="modal-footer">
      <Button @click="handleClose" class="cancel-btn">关闭</Button>
      <Button type="success" @click="handleConfirm" class="confirm-btn">会员已知晓并同意采集人脸信息</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ProtocolForAvatarDialog',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: [String, Number],
      required: false,
    },
  },
  data() {
    return {
      submitting: false,
      protocol: '<p><strong>‌1、明确告知采集目的：</strong></p><p>请清晰告知会员：“我们将使用人脸识别设备进行‌身份核验‌和‌便捷入场管理‌。”</p><p><strong>‌2、明确告知信息处理规则：</strong></p><p>请清晰告知会员：</p><p>“您的人脸信息‌仅用于本场馆的身份核验与服务管理‌。”</p><p>“我们会采取安全措施存储您的人脸信息，‌原始图像会进行加密或特征值处理‌。”</p><p>“在您会员服务到期不再续费后，或您主动要求时，我们会按法规‌删除您的人脸信息‌。”</p><p><strong>‌3、明确告知会员权利与替代方式：</strong></p><p>请清晰告知会员：</p><p>“采集人脸信息需要您的‌明确同意‌，您有权‌拒绝‌。”</p><p>“如果您不同意采集人脸信息，我们不会强制要求，并将为您提供其他入场方式，例如：‌刷会员卡‌、‌人工核验会员码‌等。”</p><p><strong>‌4、明确告知删除权：</strong></p><p>请清晰告知会员：</p><p>“在您的会员服务到期后，您有权要求我们‌删除您的个人信息（包括人脸信息）‌。”</p>',
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.initData()
        } else {
          this.resetData()
        }
      },
      immediate: false,
    },
  },
  methods: {
    initData() {
      this.submitting = false
    },
    resetData() {
      this.submitting = false
    },
    // 确认
    async handleConfirm() {
      this.submitting = true

      try {
        // 这里可以添加API调用来记录用户同意
        const res = await this.$service.post('/Web/Member/sure_face_context', {
          user_id: this.userId,
        })

        if (res.data.errorcode !== 0) {
          this.$Message.error(res.data.errormsg || '操作失败，请重试')
          return
        }

        this.$Message.success('已记录用户同意')
        this.$emit('on-success')
        this.handleClose()
      } catch (error) {
        this.$Message.error('操作失败，请重试')
      } finally {
        this.submitting = false
      }
    },
    // 关闭弹窗
    handleClose() {
      this.resetData()
      this.$emit('input', false)
    },
  },
}
</script>

<style lang="less" scoped>
.protocol-content {
  padding: 20px 0;
  line-height: 1.6;

  .privacy-notice {
    font-size: 14px;
    color: #ff6b6b;
    line-height: 1.6;
    margin-bottom: 30px;
    padding: 0;
    background: none;
    border: none;
    font-weight: 400;
  }

  .protocol-sections {
    margin-bottom: 30px;
    padding: 16px;
    hei
  }

  .bottom-notice {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
    padding: 12px 0;
    background: none;
    border: none;
    text-align: left;
    font-style: italic;
  }
}

.modal-footer {
  text-align: right;
  padding-top: 20px;

  .cancel-btn {
    margin-right: 12px;
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #666;
    min-width: 80px;
    height: 32px;
    font-weight: 400;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 16px;

    &:hover {
      background-color: #e6e6e6;
      border-color: #bfbfbf;
      color: #666;
    }
  }

  .confirm-btn {
    background-color: #52c41a;
    border-color: #52c41a;
    color: white;
    min-width: 200px;
    height: 32px;
    font-weight: 400;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 16px;

    &:hover {
      background-color: #73d13d;
      border-color: #73d13d;
    }

    &:disabled {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: #bfbfbf;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .protocol-content {
    .protocol-sections {
      .protocol-section {
        .section-content {
          padding-left: 0;
        }
      }
    }
  }

  .modal-footer {
    .confirm-btn {
      min-width: 180px;
      font-size: 13px;
    }
  }
}
</style>